# 基础镜像
FROM openjdk:8-jre
# 构建时参数
ARG APP_NAME=smart-highway-api-app
ENV JAVA_NAME=smart-highway-api-app
ENV PORT=8104
# 作者信息
LABEL maintainer="Jack"
# 设置工作目录
WORKDIR /app
# 复制项目的 JAR 包到镜像中
COPY target/${APP_NAME}.jar ${APP_NAME}.jar
# 暴露端口
EXPOSE $PORT

# 定义环境变量，允许动态传入参数
ENV JAVA_OPTS="-Xms1024m -Xmx1024m -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m -XX:MaxNewSize=512m -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8"
# 启动命令使用 ENTRYPOINT 动态传入变量
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar $JAVA_NAME.jar --spring.profiles.active=pro --server.port=$PORT"]