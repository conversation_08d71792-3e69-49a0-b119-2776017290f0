package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.CheckTeam;
import com.tunnel.service.CheckTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检测分组Controller
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@RestController
@RequestMapping("/smart/checkTeam")
@Api(tags = "检测分组管理")
public class CheckTeamController extends BaseController {
    @Autowired
    private CheckTeamService checkTeamService;

    /**
     * 查询检测分组列表
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeam:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取检测分组列表", notes = "获取检测分组数据列表")
    public TableDataInfo list(CheckTeam checkTeam) {
        startPage();
        List<CheckTeam> list = checkTeamService.selectCheckTeamList(checkTeam);
        return getDataTable(list);
    }

    /**
     * 获取检测分组详细信息
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeam:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取检测分组详细信息", notes = "根据ID获取检测分组详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(checkTeamService.selectCheckTeamById(id));
    }

    /**
     * 新增检测分组
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeam:add')")
    @Log(title = "检测分组", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增检测分组", notes = "新增检测分组")
    public AjaxResult add(@Validated @RequestBody CheckTeam checkTeam) {
        return toAjax(checkTeamService.insertCheckTeam(checkTeam));
    }

    /**
     * 修改检测分组
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeam:edit')")
    @Log(title = "检测分组", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改检测分组", notes = "修改检测分组")
    public AjaxResult edit(@Validated @RequestBody CheckTeam checkTeam) {
        return toAjax(checkTeamService.updateCheckTeam(checkTeam));
    }

    /**
     * 删除检测分组
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeam:remove')")
    @Log(title = "检测分组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除检测分组", notes = "批量删除检测分组")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkTeamService.deleteCheckTeamByIds(ids));
    }

    /**
     * 导出检测分组
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeam:export')")
    @Log(title = "检测分组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出检测分组", notes = "导出检测分组数据")
    public void export(HttpServletResponse response, CheckTeam checkTeam) {
        List<CheckTeam> list = checkTeamService.selectCheckTeamList(checkTeam);
        // 这里可以添加导出逻辑
    }

    /**
     * 查询活跃的检测分组列表
     */
    @GetMapping("/listActive")
    @ApiOperation(value = "获取活跃检测分组列表", notes = "获取状态为启用的检测分组列表")
    public AjaxResult listActive(@RequestParam(required = false) Integer type) {
        List<CheckTeam> list = checkTeamService.selectActiveCheckTeamList(type);
        return success(list);
    }

    /**
     * 修改检测分组状态
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeam:edit')")
    @Log(title = "检测分组状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    @ApiOperation(value = "修改检测分组状态", notes = "启用/停用检测分组")
    public AjaxResult changeStatus(@RequestBody CheckTeam checkTeam) {
        return toAjax(checkTeamService.updateCheckTeamStatus(checkTeam));
    }

    /**
     * 校验分组名称唯一性
     */
    @GetMapping("/checkTeamNameUnique")
    @ApiOperation(value = "校验分组名称唯一性", notes = "校验分组名称是否唯一")
    public AjaxResult checkTeamNameUnique(@RequestParam String teamName, @RequestParam(required = false) Long id) {
        CheckTeam checkTeam = new CheckTeam();
        checkTeam.setTeamName(teamName);
        checkTeam.setId(id);
        return success(checkTeamService.checkTeamNameUnique(checkTeam));
    }

}