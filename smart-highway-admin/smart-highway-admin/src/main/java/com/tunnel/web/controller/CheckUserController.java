package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.CheckUser;
import com.tunnel.service.CheckUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检测人员Controller
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@RestController
@RequestMapping("/smart/checkUser")
@Api(tags = "检测人员管理")
public class CheckUserController extends BaseController {
    @Autowired
    private CheckUserService checkUserService;

    /**
     * 查询检测人员列表
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkUser:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取检测人员列表", notes = "获取检测人员数据列表")
    public TableDataInfo list(CheckUser checkUser) {
        startPage();
        List<CheckUser> list = checkUserService.selectCheckUserList(checkUser);
        return getDataTable(list);
    }

    /**
     * 导出检测人员列表
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkUser:export')")
    @Log(title = "检测人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出检测人员", notes = "导出检测人员数据到Excel")
    public void export(HttpServletResponse response, CheckUser checkUser) {
        checkUserService.exportCheckUser(response, checkUser);
    }

    /**
     * 获取检测人员详细信息
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkUser:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取检测人员详细信息", notes = "根据ID获取检测人员详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(checkUserService.selectCheckUserById(id));
    }

    /**
     * 新增检测人员
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkUser:add')")
    @Log(title = "检测人员", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增检测人员", notes = "新增检测人员")
    public AjaxResult add(@Validated @RequestBody CheckUser checkUser) {
        return toAjax(checkUserService.insertCheckUser(checkUser));
    }

    /**
     * 修改检测人员
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkUser:edit')")
    @Log(title = "检测人员", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改检测人员", notes = "修改检测人员")
    public AjaxResult edit(@Validated @RequestBody CheckUser checkUser) {
        return toAjax(checkUserService.updateCheckUser(checkUser));
    }

    /**
     * 删除检测人员
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkUser:remove')")
    @Log(title = "检测人员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除检测人员", notes = "批量删除检测人员")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkUserService.deleteCheckUserByIds(ids));
    }

    /**
     * 根据分组ID查询检测人员列表
     */
    @GetMapping("/getByTeamId/{teamId}")
    @ApiOperation(value = "根据分组ID查询检测人员列表", notes = "根据分组ID查询检测人员列表")
    @ApiImplicitParam(name = "teamId", value = "分组ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getByTeamId(@PathVariable("teamId") Long teamId) {
        List<CheckUser> list = checkUserService.selectCheckUserListByTeamId(teamId);
        return success(list);
    }

    /**
     * 根据分组ID查询检测人员列表（备用接口）
     */
    @GetMapping("/listByTeamId/{teamId}")
    @ApiOperation(value = "根据分组ID查询检测人员列表", notes = "根据分组ID查询检测人员列表")
    @ApiImplicitParam(name = "teamId", value = "分组ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getListByTeamId(@PathVariable("teamId") Long teamId) {
        List<CheckUser> list = checkUserService.selectCheckUserListByTeamId(teamId);
        return success(list);
    }

    /**
     * 根据职位查询检测人员列表
     */
    @GetMapping("/listByPosition/{position}")
    @ApiOperation(value = "根据职位查询检测人员列表", notes = "根据职位查询检测人员列表")
    @ApiImplicitParam(name = "position", value = "职位", required = true, dataType = "String", paramType = "path")
    public AjaxResult getListByPosition(@PathVariable("position") String position) {
        List<CheckUser> list = checkUserService.selectCheckUserListByPosition(position);
        return success(list);
    }

    /**
     * 根据分组ID和职位查询检测人员列表
     */
    @GetMapping("/getByTeamIdAndPosition")
    @ApiOperation(value = "根据分组ID和职位查询检测人员列表", notes = "根据分组ID和职位查询检测人员列表，用于Word报告填充")
    public AjaxResult getByTeamIdAndPosition(@RequestParam Long teamId, @RequestParam String position) {
        List<CheckUser> list = checkUserService.selectCheckUserListByTeamIdAndPosition(teamId, position);
        return success(list);
    }

    /**
     * 根据分组ID和职位查询检测人员列表（备用接口）
     */
    @GetMapping("/listByTeamIdAndPosition/{teamId}/{position}")
    @ApiOperation(value = "根据分组ID和职位查询检测人员列表", notes = "根据分组ID和职位查询检测人员列表，用于Word报告填充")
    public AjaxResult getListByTeamIdAndPosition(@PathVariable("teamId") Long teamId, @PathVariable("position") String position) {
        List<CheckUser> list = checkUserService.selectCheckUserListByTeamIdAndPosition(teamId, position);
        return success(list);
    }

    /**
     * 批量导入检测人员数据
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkUser:import')")
    @Log(title = "检测人员", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ApiOperation(value = "批量导入检测人员数据", notes = "从Excel导入检测人员数据")
    public AjaxResult importData(@RequestPart("file") MultipartFile file) {
        String message = checkUserService.importCheckUser(file);
        return success(message);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载检测人员数据导入模板")
    public void importTemplate(HttpServletResponse response) {
        checkUserService.importTemplate(response);
    }

    /**
     * 获取下一个排序号
     */
    @GetMapping("/getNextSortNum")
    @ApiOperation(value = "获取下一个排序号", notes = "获取当前最大排序号+1")
    public AjaxResult getNextSortNum() {
        Integer nextSortNum = checkUserService.getNextSortNum();
        return success(nextSortNum);
    }

    /**
     * 批量更新人员排序
     */
    @Log(title = "检测人员排序", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateSort")
    @ApiOperation(value = "批量更新人员排序", notes = "批量更新检测人员的排序号")
    public AjaxResult batchUpdateSort(@RequestBody List<CheckUser> checkUserList) {
        return toAjax(checkUserService.batchUpdateSort(checkUserList));
    }
} 