package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.County;
import com.tunnel.service.CountyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 农村项目信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/smart/county")
@Api(tags = "农村项目管理")
public class CountyController extends BaseController {
    @Autowired
    private CountyService countyService;

    /**
     * 查询农村项目信息列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "获取农村项目信息列表", notes = "获取全部农村项目信息数据")
    @Operation(summary = "获取农村项目信息列表", description = "获取全部农村项目信息数据")
    public TableDataInfo list(County county) {
        startPage();
        List<County> list = countyService.selectCountyList(county);
        return getDataTable(list);
    }

    /**
     * 导出农村项目信息列表
     */
    @Log(title = "农村项目信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出农村项目信息", notes = "导出农村项目信息到Excel")
    @Operation(summary = "导出农村项目信息", description = "导出农村项目信息到Excel")
    public void export(HttpServletResponse response, County county) {
        countyService.exportCounty(response, county);
    }

    /**
     * 获取农村项目信息详细信息
     */
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取农村项目信息详细", notes = "根据ID获取农村项目信息详情")
    @Operation(summary = "获取农村项目信息详细", description = "根据ID获取农村项目信息详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(countyService.selectCountyById(id));
    }

    /**
     * 新增农村项目信息
     */
    @Log(title = "农村项目信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增农村项目信息", notes = "新增单条农村项目信息")
    @Operation(summary = "新增农村项目信息", description = "新增单条农村项目信息")
    public AjaxResult add(@Validated @RequestBody County county) {
        return toAjax(countyService.insertCounty(county));
    }

    /**
     * 修改农村项目信息
     */
    @Log(title = "农村项目信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改农村项目信息", notes = "修改农村项目信息")
    @Operation(summary = "修改农村项目信息", description = "修改农村项目信息")
    public AjaxResult edit(@Validated @RequestBody County county) {
        return toAjax(countyService.updateCounty(county));
    }

    /**
     * 删除农村项目信息
     */
    @Log(title = "农村项目信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除农村项目信息", notes = "批量删除农村项目信息")
    @Operation(summary = "删除农村项目信息", description = "批量删除农村项目信息")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(countyService.deleteCountyByIds(ids));
    }

    /**
     * 根据报告编号获取农村项目信息
     */
    @GetMapping("/getByReportNo/{reportNo}")
    @ApiOperation(value = "根据报告编号获取农村项目信息", notes = "根据报告编号获取农村项目信息")
    @Operation(summary = "根据报告编号获取农村项目信息", description = "根据报告编号获取农村项目信息")
    @ApiImplicitParam(name = "reportNo", value = "报告编号", required = true, dataType = "String", paramType = "path")
    public AjaxResult getByReportNo(@PathVariable("reportNo") String reportNo) {
        return success(countyService.selectCountyByReportNo(reportNo));
    }

    /**
     * 校验报告编号
     */
    @PostMapping("/checkReportNoUnique")
    @ApiOperation(value = "校验报告编号", notes = "校验报告编号是否唯一")
    @Operation(summary = "校验报告编号", description = "校验报告编号是否唯一")
    public AjaxResult checkReportNoUnique(@RequestBody County county) {
        return success(countyService.checkReportNoUnique(county));
    }

    /**
     * 批量导入农村项目数据
     */
    @Log(title = "农村项目信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ApiOperation(value = "批量导入农村项目数据", notes = "从Excel导入农村项目数据")
    public AjaxResult importData(@RequestPart("file") MultipartFile file) {
        String message = countyService.importCounty(file);
        return success(message);
    }

    /**
     * 下载导入模板
     */
    @GetMapping("/importTemplate")
    @ApiOperation(value = "下载导入模板", notes = "下载农村项目数据导入模板")
    @Operation(summary = "下载导入模板", description = "下载农村项目数据导入模板")
    public void importTemplate(HttpServletResponse response) {
        countyService.importTemplate(response);
    }

    /**
     * 获取农村项目信息记录数
     */
    @GetMapping("/count")
    @ApiOperation(value = "获取农村项目信息记录数", notes = "获取农村项目信息记录总数")
    @Operation(summary = "获取农村项目信息记录数", description = "获取农村项目信息记录总数")
    public AjaxResult count(County county) {
        int count = countyService.countCounty(county);
        return success(count);
    }
} 