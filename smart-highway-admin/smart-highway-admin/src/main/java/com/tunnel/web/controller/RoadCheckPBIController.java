package com.tunnel.web.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.RoadCheckPBI;
import com.tunnel.service.RoadCheckPBIService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.poi.xddf.usermodel.chart.*;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 路面跳车检测信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/smart/road/check/pbi")
@Api(tags = "路面跳车检测管理")
public class RoadCheckPBIController extends BaseController {
    @Autowired
    private RoadCheckPBIService roadCheckPBIService;

    /**
     * 查询路面跳车检测信息列表
     */
//    @PreAuthorize("@ss.hasPermi('road:check:bump:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取路面跳车检测信息列表", notes = "获取全部路面跳车检测信息数据")
    @Operation(summary = "获取路面跳车检测信息列表", description = "获取全部路面跳车检测信息数据")
    public TableDataInfo list(RoadCheckPBI roadCheckPBI) {
        startPage();
        List<RoadCheckPBI> list = roadCheckPBIService.selectRoadCheckBumpList(roadCheckPBI);
        return getDataTable(list);
    }

    /**
     * 导出路面跳车检测信息列表
     */
//    @PreAuthorize("@ss.hasPermi('road:check:bump:export')")
    @Log(title = "路面跳车检测信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出路面跳车检测信息", notes = "导出路面跳车检测信息到Excel")
    @Operation(summary = "导出路面跳车检测信息", description = "导出路面跳车检测信息到Excel")
    public void export(HttpServletResponse response, RoadCheckPBI roadCheckPBI) {
        roadCheckPBIService.exportOptimized(response, roadCheckPBI);
    }

    /**
     * 获取路面跳车检测信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:bump:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取路面跳车检测信息详细", notes = "根据ID获取路面跳车检测信息详情")
    @Operation(summary = "获取路面跳车检测信息详细", description = "根据ID获取路面跳车检测信息详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(roadCheckPBIService.selectRoadCheckBumpById(id));
    }

    /**
     * 新增路面跳车检测信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:bump:add')")
    @Log(title = "路面跳车检测信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增路面跳车检测信息", notes = "新增单条路面跳车检测信息")
    @Operation(summary = "新增路面跳车检测信息", description = "新增单条路面跳车检测信息")
    public AjaxResult add(@RequestBody RoadCheckPBI roadCheckPBI) {
        return toAjax(roadCheckPBIService.insertRoadCheckBump(roadCheckPBI));
    }

    /**
     * 修改路面跳车检测信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:bump:edit')")
    @Log(title = "路面跳车检测信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改路面跳车检测信息", notes = "修改路面跳车检测信息")
    @Operation(summary = "修改路面跳车检测信息", description = "修改路面跳车检测信息")
    public AjaxResult edit(@RequestBody RoadCheckPBI roadCheckPBI) {
        return toAjax(roadCheckPBIService.updateRoadCheckBump(roadCheckPBI));
    }

    /**
     * 删除路面跳车检测信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:bump:remove')")
    @Log(title = "路面跳车检测信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除路面跳车检测信息", notes = "删除路面跳车检测信息")
    @Operation(summary = "删除路面跳车检测信息", description = "删除路面跳车检测信息")
    @ApiImplicitParam(name = "ids", value = "主键ID集合（多个ID用逗号分隔）", required = true, dataType = "Long", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(roadCheckPBIService.deleteRoadCheckBumpByIds(ids));
    }

    /**
     * 根据道路ID获取路面跳车检测信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:bump:list')")
    @GetMapping("/listByRoadId/{roadId}")
    @ApiOperation(value = "根据道路ID获取路面跳车检测信息", notes = "根据道路ID获取路面跳车检测信息列表")
    @Operation(summary = "根据道路ID获取路面跳车检测信息", description = "根据道路ID获取路面跳车检测信息列表")
    @ApiImplicitParam(name = "roadId", value = "道路ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getListByRoadId(@PathVariable("roadId") Long roadId) {
        List<RoadCheckPBI> list = roadCheckPBIService.selectRoadCheckBumpByRoadId(roadId);
        return success(list);
    }

    /**
     * 批量导入路面跳车检测数据
     */
    @Log(title = "路面跳车检测信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ApiOperation(value = "批量导入路面跳车检测数据", notes = "从Excel导入路面跳车检测数据")
    @Anonymous
    public AjaxResult importData(@RequestPart("file") MultipartFile file, Long roadId) {
        return success(roadCheckPBIService.batchImport(file, roadId));
    }

    /**
     * 获取路面跳车检测信息记录数
     */
//    @PreAuthorize("@ss.hasPermi('road:check:bump:list')")
    @GetMapping("/count")
    @ApiOperation(value = "获取路面跳车检测信息记录数", notes = "获取路面跳车检测信息记录总数")
    @Operation(summary = "获取路面跳车检测信息记录数", description = "获取路面跳车检测信息记录总数")
    public AjaxResult count(RoadCheckPBI roadCheckPBI) {
        int count = roadCheckPBIService.countRoadCheckBump(roadCheckPBI);
        return success(count);
    }

    /**
     * 获取导入模板
     */
    @ApiOperation(value = "获取导入模板", notes = "下载路面跳车检测数据导入模板")
//    @PreAuthorize("@ss.hasPermi('road:check:bump:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ClassPathResource resource = new ClassPathResource("static/pbi-template.xlsx");
        InputStream inputStream = resource.getInputStream();
        String fileName = "路面跳车检测数据导入模板.xlsx";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }

    @ApiOperation(value = "导出PBI数据", notes = "导出PBI数据，上行下行分开显示")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @PostMapping("/exportPBI")
    public void exportPBIByDirection(HttpServletResponse response, @RequestParam("roadId") Long roadId) {
        roadCheckPBIService.exportPBIByDirection(response, roadId);
    }


    @ApiOperation(value = "导出Word报告PBI", notes = "导出Word报告PBI")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @PostMapping("/exportWordPBI")
    public void exportWordPBI(HttpServletResponse response, 
                             @RequestParam("roadId") Long roadId,
                             @RequestParam(value = "teamId", required = false) Long teamId,
                             @RequestParam(value = "dateTime", required = false) String dateTime,
                             @RequestParam(value = "monthDate", required = false) String monthDate,
                             @RequestParam(value = "titleName", required = false) String titleName,
                             @RequestParam(value = "checkName", required = false) String checkName,
                             @RequestParam(value = "reviewName", required = false) String reviewName) {
        roadCheckPBIService.exportWordPBIByDirection(response, roadId, teamId, dateTime, monthDate, titleName, checkName, reviewName);
    }


} 