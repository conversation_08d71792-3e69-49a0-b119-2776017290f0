package com.tunnel.web.controller;

import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.CheckTeamUser;
import com.tunnel.service.CheckTeamUserService;
import com.tunnel.web.controller.dto.BatchAddUserToTeamsDTO;
import com.tunnel.web.controller.dto.BatchAddUsersToTeamDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 检测分组用户关联Controller
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@RestController
@RequestMapping("/smart/checkTeamUser")
@Api(tags = "检测分组用户关联管理")
public class CheckTeamUserController extends BaseController {
    @Autowired
    private CheckTeamUserService checkTeamUserService;

    /**
     * 查询检测分组用户关联列表
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取检测分组用户关联列表", notes = "获取检测分组用户关联数据列表")
    public TableDataInfo list(CheckTeamUser checkTeamUser) {
        startPage();
        List<CheckTeamUser> list = checkTeamUserService.selectCheckTeamUserList(checkTeamUser);
        return getDataTable(list);
    }

    /**
     * 导出检测分组用户关联列表
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:export')")
    @Log(title = "检测分组用户关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出检测分组用户关联", notes = "导出检测分组用户关联数据到Excel")
    public void export(HttpServletResponse response, CheckTeamUser checkTeamUser) {
        checkTeamUserService.exportCheckTeamUser(response, checkTeamUser);
    }

    /**
     * 获取检测分组用户关联详细信息
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取检测分组用户关联详细信息", notes = "根据ID获取检测分组用户关联详情")
    @ApiImplicitParam(name = "id", value = "主键ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(checkTeamUserService.selectCheckTeamUserById(id));
    }

    /**
     * 新增检测分组用户关联
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:add')")
    @Log(title = "检测分组用户关联", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增检测分组用户关联", notes = "新增检测分组用户关联")
    public AjaxResult add(@Validated @RequestBody CheckTeamUser checkTeamUser) {
        return toAjax(checkTeamUserService.insertCheckTeamUser(checkTeamUser));
    }

    /**
     * 修改检测分组用户关联
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:edit')")
    @Log(title = "检测分组用户关联", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改检测分组用户关联", notes = "修改检测分组用户关联")
    public AjaxResult edit(@Validated @RequestBody CheckTeamUser checkTeamUser) {
        return toAjax(checkTeamUserService.updateCheckTeamUser(checkTeamUser));
    }

    /**
     * 删除检测分组用户关联
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:remove')")
    @Log(title = "检测分组用户关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除检测分组用户关联", notes = "批量删除检测分组用户关联")
    @ApiImplicitParam(name = "ids", value = "主键ID数组", required = true, dataType = "Long[]", paramType = "path")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(checkTeamUserService.deleteCheckTeamUserByIds(ids));
    }

    /**
     * 根据分组ID查询关联的用户列表
     */
    @GetMapping("/getUsersByTeamId/{teamId}")
    @ApiOperation(value = "根据分组ID查询关联的用户列表", notes = "根据分组ID查询关联的用户列表")
    @ApiImplicitParam(name = "teamId", value = "分组ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getUsersByTeamId(@PathVariable("teamId") Long teamId, @RequestParam(required = false) Integer type) {
        List<CheckTeamUser> list = checkTeamUserService.selectUsersByTeamId(teamId, type);
        return success(list);
    }

    /**
     * 根据用户ID查询关联的分组列表
     */
    @GetMapping("/getTeamsByUserId/{userId}")
    @ApiOperation(value = "根据用户ID查询关联的分组列表", notes = "根据用户ID查询关联的分组列表")
    @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getTeamsByUserId(@PathVariable("userId") Long userId, @RequestParam(required = false) Integer type) {
        List<CheckTeamUser> list = checkTeamUserService.selectTeamsByUserId(userId, type);
        return success(list);
    }

    /**
     * 批量添加用户到分组
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:add')")
    @Log(title = "检测分组用户关联", businessType = BusinessType.INSERT)
    @PostMapping("/batchAddUsersToTeam")
    @ApiOperation(value = "批量添加用户到分组", notes = "批量添加多个用户到指定分组")
    public AjaxResult batchAddUsersToTeam(@RequestBody BatchAddUsersToTeamDTO request) {
        int result = checkTeamUserService.batchAddUsersToTeam(request.getTeamId(), request.getUserIds());
        return toAjax(result);
    }

    /**
     * 批量添加用户到多个分组
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:add')")
    @Log(title = "检测分组用户关联", businessType = BusinessType.INSERT)
    @PostMapping("/batchAddUserToTeams")
    @ApiOperation(value = "批量添加用户到多个分组", notes = "批量添加指定用户到多个分组")
    public AjaxResult batchAddUserToTeams(@RequestBody BatchAddUserToTeamsDTO request) {
        int result = checkTeamUserService.batchAddUserToTeams(request.getUserId(), request.getTeamIds());
        return toAjax(result);
    }

    /**
     * 移除用户分组关联
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:remove')")
    @Log(title = "检测分组用户关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeUserFromTeam")
    @ApiOperation(value = "移除用户分组关联", notes = "移除指定用户与分组的关联关系")
    public AjaxResult removeUserFromTeam(@RequestParam Long teamId, @RequestParam Long userId) {
        int result = checkTeamUserService.removeUserFromTeam(teamId, userId);
        return toAjax(result);
    }

    /**
     * 检查用户分组关联是否存在
     */
    @GetMapping("/checkExists")
    @ApiOperation(value = "检查用户分组关联是否存在", notes = "检查指定用户与分组的关联关系是否存在")
    public AjaxResult checkExists(@RequestParam Long teamId, @RequestParam Long userId) {
        boolean exists = checkTeamUserService.checkTeamUserExists(teamId, userId);
        return success(exists);
    }

    /**
     * 批量更新排序
     */
//    @PreAuthorize("@ss.hasPermi('smart:checkTeamUser:edit')")
    @Log(title = "检测分组用户关联排序", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateSort")
    @ApiOperation(value = "批量更新排序", notes = "批量更新检测分组用户关联的排序号")
    public AjaxResult batchUpdateSort(@RequestBody List<CheckTeamUser> checkTeamUserList) {
        return toAjax(checkTeamUserService.batchUpdateSort(checkTeamUserList));
    }

    /**
     * 获取分组内下一个排序号
     */
    @GetMapping("/getNextSortNum/{teamId}")
    @ApiOperation(value = "获取分组内下一个排序号", notes = "获取指定分组内当前最大排序号+1")
    @ApiImplicitParam(name = "teamId", value = "分组ID", required = true, dataType = "Long", paramType = "path")
    public AjaxResult getNextSortNum(@PathVariable("teamId") Long teamId) {
        Integer nextSortNum = checkTeamUserService.getNextSortNumByTeamId(teamId);
        return success(nextSortNum);
    }
} 