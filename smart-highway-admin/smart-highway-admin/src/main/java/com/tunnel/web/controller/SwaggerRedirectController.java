package com.tunnel.web.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * Swagger UI重定向控制器
 * 
 * <AUTHOR>
 */
@Controller
public class SwaggerRedirectController {
    
    /**
     * 将/swagger重定向到/swagger-ui/index.html
     */
    @GetMapping("/swagger")
    public String redirectToSwaggerUi() {
        return "redirect:/swagger-ui/";
    }
    
    /**
     * 将/swagger/index.html重定向到/swagger-ui/index.html
     */
    @GetMapping("/swagger/index.html")
    public String redirectSwaggerIndexToSwaggerUi() {
        return "redirect:/swagger-ui/";
    }
    
    /**
     * 将/swagger-ui.html重定向到/swagger-ui/index.html
     */
    @GetMapping("/swagger-ui.html")
    public String redirectSwaggerUiHtmlToSwaggerUi() {
        return "redirect:/swagger-ui/";
    }
    
    /**
     * 将/doc.html重定向到/swagger-ui/index.html
     * 兼容knife4j路径
     */
    @GetMapping("/doc.html")
    public String redirectDocToSwaggerUi() {
        return "redirect:/swagger-ui/";
    }
} 