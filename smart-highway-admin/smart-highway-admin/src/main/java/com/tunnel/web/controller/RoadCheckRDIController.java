package com.tunnel.web.controller;

import com.tunnel.common.annotation.Anonymous;
import com.tunnel.common.annotation.Log;
import com.tunnel.common.core.controller.BaseController;
import com.tunnel.common.core.domain.AjaxResult;
import com.tunnel.common.core.page.TableDataInfo;
import com.tunnel.common.enums.BusinessType;
import com.tunnel.domain.RoadCheckRDI;
import com.tunnel.service.RoadCheckRDIService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.List;

/**
 * 路面车辙深度检测信息Controller
 * 
 * <AUTHOR>
 * @date 2024-12-02
 */
@RestController
@RequestMapping("/smart/road/check/rd")
@Api(tags = "路面车辙深度检测管理")
public class RoadCheckRDIController extends BaseController {
    @Autowired
    private RoadCheckRDIService roadCheckRDIService;

    /**
     * 查询路面车辙深度检测信息列表
     */
//    @PreAuthorize("@ss.hasPermi('road:check:rd:list')")
    @GetMapping("/list")
    @ApiOperation(value = "获取路面车辙深度检测信息列表", notes = "获取全部路面车辙深度检测信息数据")
    public TableDataInfo list(RoadCheckRDI roadCheckRDI) {
        startPage();
        List<RoadCheckRDI> list = roadCheckRDIService.selectScRoadCheckRdList(roadCheckRDI);
        return getDataTable(list);
    }

    /**
     * 导出路面车辙深度检测信息列表
     */
//    @PreAuthorize("@ss.hasPermi('road:check:rd:export')")
    @Log(title = "路面车辙深度检测信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation(value = "导出路面车辙深度检测信息", notes = "导出路面车辙深度检测信息到Excel")
    public void export(HttpServletResponse response, RoadCheckRDI roadCheckRDI) {
        roadCheckRDIService.exportOptimized(response, roadCheckRDI);
    }

    /**
     * 获取路面车辙深度检测信息详细信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:rd:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation(value = "获取路面车辙深度检测信息详细", notes = "根据ID获取路面车辙深度检测信息详情")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(roadCheckRDIService.selectScRoadCheckRdById(id));
    }

    /**
     * 新增路面车辙深度检测信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:rd:add')")
    @Log(title = "路面车辙深度检测信息", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation(value = "新增路面车辙深度检测信息", notes = "新增单条路面车辙深度检测信息")
    public AjaxResult add(@RequestBody RoadCheckRDI roadCheckRDI) {
        return toAjax(roadCheckRDIService.insertScRoadCheckRd(roadCheckRDI));
    }

    /**
     * 修改路面车辙深度检测信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:rd:edit')")
    @Log(title = "路面车辙深度检测信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation(value = "修改路面车辙深度检测信息", notes = "修改路面车辙深度检测信息")
    public AjaxResult edit(@RequestBody RoadCheckRDI roadCheckRDI) {
        return toAjax(roadCheckRDIService.updateScRoadCheckRd(roadCheckRDI));
    }

    /**
     * 删除路面车辙深度检测信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:rd:remove')")
    @Log(title = "路面车辙深度检测信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation(value = "删除路面车辙深度检测信息", notes = "删除路面车辙深度检测信息")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(roadCheckRDIService.deleteScRoadCheckRdByIds(ids));
    }

    /**
     * 根据道路ID获取路面车辙深度检测信息
     */
//    @PreAuthorize("@ss.hasPermi('road:check:rd:list')")
    @GetMapping("/listByRoadId/{roadId}")
    @ApiOperation(value = "根据道路ID获取路面车辙深度检测信息", notes = "根据道路ID获取路面车辙深度检测信息列表")
    public AjaxResult getListByRoadId(@PathVariable("roadId") Long roadId) {
        List<RoadCheckRDI> list = roadCheckRDIService.selectScRoadCheckRdByRoadId(roadId);
        return success(list);
    }

    /**
     * 批量导入路面车辙深度检测数据
     */
    @Log(title = "路面车辙深度检测信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ApiOperation(value = "批量导入路面车辙深度检测数据", notes = "从Excel导入路面车辙深度检测数据")
    @Anonymous
    public AjaxResult importData(@RequestPart("file") MultipartFile file, Long roadId) {
        return success(roadCheckRDIService.batchImport(file, roadId));
    }

    /**
     * 获取路面车辙深度检测信息记录数
     */
//    @PreAuthorize("@ss.hasPermi('road:check:rd:list')")
    @GetMapping("/count")
    @ApiOperation(value = "获取路面车辙深度检测信息记录数", notes = "获取路面车辙深度检测信息记录总数")
    @Operation(summary = "获取路面车辙深度检测信息记录数", description = "获取路面车辙深度检测信息记录总数")
    public AjaxResult count(RoadCheckRDI roadCheckRDI) {
        int count = roadCheckRDIService.countScRoadCheckRd(roadCheckRDI);
        return success(count);
    }

    /**
     * 获取导入模板
     */
    @ApiOperation(value = "获取导入模板", notes = "下载路面车辙深度检测数据导入模板")
//    @PreAuthorize("@ss.hasPermi('road:check:rd:import')")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ClassPathResource resource = new ClassPathResource("static/rdi-template.xlsx");
        InputStream inputStream = resource.getInputStream();
        String fileName = "路面车辙深度检测数据导入模板.xlsx";
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
        FileCopyUtils.copy(inputStream, response.getOutputStream());
    }



    @ApiOperation(value = "导出RDI数据", notes = "导出RDI数据，上行下行合并显示")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @PostMapping("/exportRDI")
    public void exportRDIByDirection(HttpServletResponse response, @RequestParam("roadId") Long roadId) {
        roadCheckRDIService.exportRDIByDirection(response, roadId);
    }


    @ApiOperation(value = "导出Word报告RDI", notes = "导出Word报告RDI")
//    @PreAuthorize("@ss.hasPermi('road:check:export')")
    @PostMapping("/exportWordRDI")
    public void exportWordRDI(HttpServletResponse response, 
                             @RequestParam("roadId") Long roadId,
                             @RequestParam(value = "teamId", required = false) Long teamId,
                             @RequestParam(value = "dateTime", required = false) String dateTime,
                             @RequestParam(value = "monthDate", required = false) String monthDate,
                             @RequestParam(value = "titleName", required = false) String titleName,
                             @RequestParam(value = "checkName", required = false) String checkName,
                             @RequestParam(value = "reviewName", required = false) String reviewName) {
        roadCheckRDIService.exportWordRDIByDirection(response, roadId, teamId, dateTime, monthDate, titleName, checkName, reviewName);
    }
} 